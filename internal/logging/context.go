// Package logging provides context utilities for accessing request loggers
// from Gin contexts and other request processing contexts.
package logging

import (
	"context"

	"github.com/gin-gonic/gin"
)

// GetRequestLoggerFromGinContext retrieves the request logger from a Gin context.
// Returns nil if no logger is found or if logging is disabled.
func GetRequestLoggerFromGinContext(c *gin.Context) RequestLogger {
	if logger, exists := c.Get("request_logger"); exists {
		if requestLogger, ok := logger.(RequestLogger); ok {
			return requestLogger
		}
	}
	return nil
}

// GetRequestLoggerFromContext retrieves the request logger from a standard context.
// Returns nil if no logger is found or if logging is disabled.
func GetRequestLoggerFromContext(ctx context.Context) RequestLogger {
	if logger := ctx.Value("request_logger"); logger != nil {
		if requestLogger, ok := logger.(RequestLogger); ok {
			return requestLogger
		}
	}
	return nil
}

// WithRequestLogger adds a request logger to a context.
func WithRequestLogger(ctx context.Context, logger RequestLogger) context.Context {
	return context.WithValue(ctx, "request_logger", logger)
}
